# Demo Programs

这个目录包含三个演示程序：

## 1. Time Demo (main.go)

一个简单的Go程序，用于打印当前时间信息。

## 2. Logger Demo (logger-demo.go)

一个演示CCE网络项目中logger使用方式的程序，参考了 `cce-network-v2/plugins/roce/ipam_client.go` 中 `allocateRDMAIPsWithCCEAgent` 函数的logger使用方式。

## 3. RoCE Logger Demo (roce-logger-demo.go)

一个完全基于 `cce-network-v2/plugins/roce/roce.go` 实现的日志打印demo，使用与roce插件完全相同的日志系统。

## 功能特性

- 使用 logrus 进行格式化日志输出
- 使用 cobra 提供命令行界面
- 显示多种时间格式
- 支持版本信息查询
- 编译为Linux可执行文件

## 依赖

本项目使用了代码库中已有的依赖版本：

- Go 1.20
- github.com/sirupsen/logrus v1.8.1
- github.com/spf13/cobra v1.6.0
- github.com/google/uuid v1.3.0
- github.com/satori/go.uuid v1.2.0 (用于roce-logger-demo)
- github.com/baidubce/baiducloud-cce-cni-driver/cce-network-v2 (本地模块)

## 编译

### 编译Time Demo

#### 编译为Linux可执行文件
```bash
cd demo
GOOS=linux GOARCH=amd64 go build -o time-demo-linux .
```

#### 编译为本地可执行文件
```bash
cd demo
go build -o time-demo .
```

### 编译Logger Demo

#### 编译为Linux可执行文件
```bash
cd demo
GOOS=linux GOARCH=amd64 go build -o logger-demo-linux logger-demo.go
```

#### 编译为本地可执行文件
```bash
cd demo
go build -o logger-demo-local logger-demo.go
```

### 编译RoCE Logger Demo

#### 使用Makefile编译（推荐）
```bash
cd demo
# 编译Linux版本
make roce-logger-demo-linux
# 编译本地版本
make roce-logger-demo
# 编译所有demo的Linux版本
make linux
```

#### 手动编译为Linux可执行文件
```bash
cd demo
GOOS=linux GOARCH=amd64 CGO_ENABLED=0 go build -o roce-logger-demo-linux roce-logger-demo.go
```

#### 编译为本地可执行文件
```bash
cd demo
go build -o roce-logger-demo roce-logger-demo.go
```

## 使用方法

### Time Demo

#### 运行时间demo
```bash
./time-demo-linux
```

输出示例：
```
INFO[2025-08-01 16:13:13] Current time information                      timestamp=1754035993 timezone=Local weekday=Friday
=== Current Time Demo ===
Current time: 2025-08-01 16:13:13
RFC3339 format: 2025-08-01T16:13:13+08:00
Unix timestamp: 1754035993
Timezone: Local
Weekday: Friday
Year: 2025, Month: August, Day: 1
Hour: 16, Minute: 13, Second: 13
INFO[2025-08-01 16:13:13] Time demo completed successfully
```

#### 查看版本信息
```bash
./time-demo-linux version
```

#### 查看帮助信息
```bash
./time-demo-linux --help
```

### Logger Demo

#### 运行logger demo（默认格式，与CCE网络项目一致）
```bash
./logger-demo-linux
```

输出示例（无时间戳，与CCE网络项目保持一致）：
```
Using CCE network logger format (no timestamp, same as original)
level=info msg="====> Logger Demo Begins <====" component=demo plugin=logger-demo reqID=a2457d56-0530-4798-9e7d-9b783ea92cb2 version=1.0.0
level=info msg="Allocated RDMAIPAM for pod demo-namespace/demo-pod in ns /var/run/netns/demo-netns for container demo-container-12345" component=demo plugin=logger-demo reqID=a2457d56-0530-4798-9e7d-9b783ea92cb2 version=1.0.0
...
```

#### 运行logger demo（带时间戳版本）
```bash
./logger-demo-linux --timestamp
```

输出示例（带时间戳）：
```
Using custom logger with timestamp (for demonstration)
time="2025-08-01 17:16:17" level=info msg="====> Logger Demo Begins <====" component=demo plugin=logger-demo reqID=0c2654d3-8b4f-4336-a1bd-56a74cb89e7e version=1.0.0
time="2025-08-01 17:16:17" level=info msg="Allocated RDMAIPAM for pod demo-namespace/demo-pod in ns /var/run/netns/demo-netns for container demo-container-12345" component=demo plugin=logger-demo reqID=0c2654d3-8b4f-4336-a1bd-56a74cb89e7e version=1.0.0
...
```

#### 查看版本信息
```bash
./logger-demo-linux version
```

#### 查看帮助信息
```bash
./logger-demo-linux --help
```

### RoCE Logger Demo

#### 基本使用（输出到stdout）
```bash
./roce-logger-demo-linux
```

输出示例：
```
Logging to stdout
INFO[0000]roce-logger-demo.go:137 main.runRoceLoggerDemo ====> CmdAdd Begins: containerID: demo-container-12345, netns: /var/run/netns/demo-netns, ifName: eth0, args: K8S_POD_NAMESPACE=default;K8S_POD_NAME=demo-pod, path: /opt/cni/bin  method=DEMO reqID=7c761733-890c-4f72-af22-40f2404a1c21
INFO[0000]roce-logger-demo.go:178 main.simulateNetworkOperations create ipvlan args masterMac: aa:bb:cc:dd:ee:ff,ifName:rdma1,tmpName:tmp-rdma1,MTU:1500,ParentIndex:2  method=DEMO reqID=7c761733-890c-4f72-af22-40f2404a1c21
...
```

#### 输出到文件
```bash
./roce-logger-demo-linux --file
# 日志文件位置: /tmp/cce-roce-demo.log
```

文件输出示例：
```
time="2025-08-01 22:10:00" level=info msg="====> CmdAdd Begins: containerID: demo-container-12345, netns: /var/run/netns/demo-netns, ifName: eth0, args: K8S_POD_NAMESPACE=default;K8S_POD_NAME=demo-pod, path: /opt/cni/bin" func=main.runRoceLoggerDemo file="roce-logger-demo.go:137" method=DEMO reqID=81162c6c-7f51-4f07-a346-b682eb0e525e
```

#### 设置日志级别
```bash
./roce-logger-demo-linux --level debug
./roce-logger-demo-linux --level warn
./roce-logger-demo-linux --level error
```

#### 查看版本信息
```bash
./roce-logger-demo-linux version
```

#### 查看帮助信息
```bash
./roce-logger-demo-linux --help
```

## 文件说明

- `main.go` - Time Demo主程序文件
- `logger-demo.go` - Logger Demo主程序文件
- `roce-logger-demo.go` - RoCE Logger Demo主程序文件
- `Makefile` - 构建脚本，支持交叉编译
- `go.mod` - Go模块依赖文件
- `time-demo-linux` - Time Demo编译后的Linux可执行文件
- `logger-demo-linux` - Logger Demo编译后的Linux可执行文件
- `roce-logger-demo-linux` - RoCE Logger Demo编译后的Linux可执行文件
- `README.md` - 本说明文件

## 特性说明

### Time Demo特性
程序会输出以下时间信息：
- 标准格式时间
- RFC3339格式时间
- Unix时间戳
- 时区信息
- 星期几
- 年、月、日详细信息
- 时、分、秒详细信息

同时使用logrus记录结构化日志，包含时间戳、时区和星期信息。

### Logger Demo特性
演示CCE网络项目中的日志系统使用方式：
- 使用 `logging.SetupCNILogging()` 设置syslog输出
- 使用 `logging.DefaultLogger.WithFields()` 创建结构化logger
- 模拟 `allocateRDMAIPsWithCCEAgent` 函数中的 `logger.Infof` 调用
- 使用 `logfields` 包中的常量进行标准化字段命名
- 演示不同日志级别（Debug, Info, Warn, Error）
- 演示结构化日志记录和时间相关的日志字段
- 每个日志条目都包含reqID、plugin、component等标识字段

### RoCE Logger Demo特性
完全基于 `roce.go` 实现的日志系统演示：
- **完全相同的setupLogging函数**：与 `roce.go` 中的实现完全一致
- **相同的日志格式**：时间戳格式 "2006-01-02 15:03:04"
- **相同的调用者信息**：显示函数名和文件:行号
- **相同的结构化字段**：reqID (使用satori/go.uuid) 和 method
- **相同的日志消息**：复制了 `roce.go` 中的实际日志消息
- **文件输出支持**：支持输出到文件或stdout
- **多日志级别**：支持debug、info、warn、error级别
- **网络操作模拟**：模拟ipvlan创建、路由添加等网络操作的日志
- **错误处理演示**：展示各种错误情况的日志输出
- **panic恢复**：包含与 `roce.go` 相同的panic恢复机制

### 关于时间戳的说明
**为什么默认没有时间戳？**

CCE网络项目的日志配置中故意禁用了时间戳显示（`DisableTimestamp: true`），原因是：

1. **Syslog处理时间戳**：当使用 `SetupCNILogging()` 时，日志会输出到syslog，而syslog系统会自动添加时间戳
2. **避免重复时间戳**：防止应用程序和系统日志都添加时间戳造成重复
3. **统一日志格式**：在容器和Kubernetes环境中，通常由日志收集系统统一处理时间戳

本demo提供了两种模式：
- 默认模式：与CCE网络项目完全一致（无时间戳）
- 时间戳模式：使用 `--timestamp` 参数启用时间戳显示（用于演示对比）
