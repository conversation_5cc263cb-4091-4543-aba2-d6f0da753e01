# Makefile for demo applications
# 支持编译为Linux可执行二进制文件

# 变量定义
GO := go
GOOS := linux
GOARCH := amd64
CGO_ENABLED := 0

# 输出目录
OUTPUT_DIR := .
LINUX_SUFFIX := -linux

# 目标文件
TARGETS := logger-demo roce-logger-demo main time-demo
LINUX_TARGETS := $(addsuffix $(LINUX_SUFFIX), $(TARGETS))

# 默认目标
.PHONY: all
all: $(TARGETS)

# Linux目标
.PHONY: linux
linux: $(LINUX_TARGETS)

# 清理目标
.PHONY: clean
clean:
	rm -f $(TARGETS) $(LINUX_TARGETS)

# 本地编译规则
logger-demo: logger-demo.go
	$(GO) build -o $@ $<

roce-logger-demo: roce-logger-demo.go
	$(GO) build -o $@ $<

main: main.go
	$(GO) build -o $@ $<

time-demo: time-demo.go
	$(GO) build -o $@ $<

# Linux交叉编译规则
logger-demo$(LINUX_SUFFIX): logger-demo.go
	GOOS=$(GOOS) GOARCH=$(GOARCH) CGO_ENABLED=$(CGO_ENABLED) $(GO) build -o $@ $<

roce-logger-demo$(LINUX_SUFFIX): roce-logger-demo.go
	GOOS=$(GOOS) GOARCH=$(GOARCH) CGO_ENABLED=$(CGO_ENABLED) $(GO) build -o $@ $<

main$(LINUX_SUFFIX): main.go
	GOOS=$(GOOS) GOARCH=$(GOARCH) CGO_ENABLED=$(CGO_ENABLED) $(GO) build -o $@ $<

time-demo$(LINUX_SUFFIX): time-demo.go
	GOOS=$(GOOS) GOARCH=$(GOARCH) CGO_ENABLED=$(CGO_ENABLED) $(GO) build -o $@ $<

# 创建time-demo.go文件（如果不存在）
time-demo.go:
	@echo "package main" > $@
	@echo "" >> $@
	@echo "import (" >> $@
	@echo "	\"fmt\"" >> $@
	@echo "	\"time\"" >> $@
	@echo ")" >> $@
	@echo "" >> $@
	@echo "func main() {" >> $@
	@echo "	fmt.Printf(\"Current time: %s\\n\", time.Now().Format(\"2006-01-02 15:04:05\"))" >> $@
	@echo "}" >> $@

# 帮助信息
.PHONY: help
help:
	@echo "Available targets:"
	@echo "  all          - Build all demos for current platform"
	@echo "  linux        - Build all demos for Linux (cross-compile)"
	@echo "  clean        - Remove all built binaries"
	@echo "  help         - Show this help message"
	@echo ""
	@echo "Individual targets:"
	@echo "  logger-demo       - Build logger demo for current platform"
	@echo "  roce-logger-demo  - Build roce logger demo for current platform"
	@echo "  main             - Build main demo for current platform"
	@echo "  time-demo        - Build time demo for current platform"
	@echo ""
	@echo "Linux targets (add -linux suffix):"
	@echo "  logger-demo-linux       - Build logger demo for Linux"
	@echo "  roce-logger-demo-linux  - Build roce logger demo for Linux"
	@echo "  main-linux             - Build main demo for Linux"
	@echo "  time-demo-linux        - Build time demo for Linux"
	@echo ""
	@echo "Usage examples:"
	@echo "  make roce-logger-demo-linux    # Build roce logger demo for Linux"
	@echo "  make linux                     # Build all demos for Linux"
	@echo "  make clean                     # Clean all built files"

# 测试目标
.PHONY: test
test: roce-logger-demo
	@echo "Testing roce-logger-demo..."
	./roce-logger-demo --help
	@echo ""
	@echo "Running demo with stdout output..."
	./roce-logger-demo
	@echo ""
	@echo "Running demo with file output..."
	./roce-logger-demo --file
	@echo "Check log file: /tmp/cce-roce-demo.log"

.PHONY: test-linux
test-linux: roce-logger-demo-linux
	@echo "Linux binary built successfully: roce-logger-demo-linux"
	@echo "To test on Linux system, copy the binary and run:"
	@echo "  ./roce-logger-demo-linux --help"
	@echo "  ./roce-logger-demo-linux"
	@echo "  ./roce-logger-demo-linux --file"
