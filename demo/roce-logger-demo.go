/*
 * Copyright (c) 2021 Baidu, Inc. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file
 * except in compliance with the License. You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under the
 * License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND,
 * either express or implied. See the License for the specific language governing permissions
 * and limitations under the License.
 *
 */

package main

import (
	"fmt"
	"os"
	"path"
	"path/filepath"
	"runtime"
	"runtime/debug"

	uuid "github.com/satori/go.uuid"
	"github.com/sirupsen/logrus"
	"github.com/spf13/cobra"
)

const (
	// 日志文件路径，参考roce.go中的logFile常量
	logFile = "/tmp/cce-roce-demo.log"
	version = "1.0.0"
)

var (
	// 命令行选项
	enableFileOutput bool
	logLevel         string
	
	// 根命令
	rootCmd = &cobra.Command{
		Use:   "roce-logger-demo",
		Short: "A demo application using the same logging system as roce.go",
		Long:  `A demo application that demonstrates the exact same logging system used in the roce CNI plugin.`,
		Run:   runRoceLoggerDemo,
	}
)

func init() {
	// 锁定OS线程，与roce.go中的init函数保持一致
	runtime.LockOSThread()
	
	// 添加命令行选项
	rootCmd.Flags().BoolVar(&enableFileOutput, "file", false, "Enable file output (default: false, output to stdout)")
	rootCmd.Flags().StringVar(&logLevel, "level", "info", "Log level (debug, info, warn, error)")
	
	// 添加版本命令
	rootCmd.AddCommand(&cobra.Command{
		Use:   "version",
		Short: "Print version information",
		Run: func(cmd *cobra.Command, args []string) {
			fmt.Printf("roce-logger-demo version %s\n", version)
		},
	})
}

// setupLogging 完全参考roce.go中的setupLogging函数实现
func setupLogging(method string) (*logrus.Entry, error) {
	logrus.SetReportCaller(true)
	logrus.SetFormatter(&logrus.TextFormatter{
		TimestampFormat: "2006-01-02 15:03:04",
		CallerPrettyfier: func(frame *runtime.Frame) (function string, file string) {
			fileName := path.Base(frame.File)
			return frame.Function, fmt.Sprintf("%s:%d", fileName, frame.Line)
		},
	})

	// 设置日志级别
	switch logLevel {
	case "debug":
		logrus.SetLevel(logrus.DebugLevel)
	case "info":
		logrus.SetLevel(logrus.InfoLevel)
	case "warn":
		logrus.SetLevel(logrus.WarnLevel)
	case "error":
		logrus.SetLevel(logrus.ErrorLevel)
	default:
		logrus.SetLevel(logrus.InfoLevel)
	}

	if enableFileOutput {
		// 创建日志目录，与roce.go中的逻辑一致
		logDir := filepath.Dir(logFile)
		if err := os.MkdirAll(logDir, 0755); err != nil && !os.IsExist(err) {
			return nil, err
		}

		file, err := os.OpenFile(logFile, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
		if err != nil {
			return nil, err
		}
		logrus.SetOutput(file)
	}

	// 创建带有字段的logger，与roce.go中的格式完全一致
	loggerEntry := logrus.WithFields(logrus.Fields{
		"reqID":  uuid.NewV4().String(),
		"method": method,
	})

	return loggerEntry, nil
}

func runRoceLoggerDemo(cmd *cobra.Command, args []string) {
	logger, err := setupLogging("DEMO")
	if err != nil {
		fmt.Printf("Failed to set up logging: %v\n", err)
		return
	}

	if enableFileOutput {
		fmt.Printf("Logging to file: %s\n", logFile)
	} else {
		fmt.Println("Logging to stdout")
	}

	// 模拟roce.go中cmdAdd函数的日志输出格式
	containerID := "demo-container-12345"
	netns := "/var/run/netns/demo-netns"
	ifName := "eth0"
	args_str := "K8S_POD_NAMESPACE=default;K8S_POD_NAME=demo-pod"
	path_str := "/opt/cni/bin"

	logger.Infof("====> CmdAdd Begins: containerID: %v, netns: %v, ifName: %v, args: %v, path: %v",
		containerID, netns, ifName, args_str, path_str)
	defer logger.Infof("====> CmdAdd Ends <====")

	// 添加defer函数来捕获panic，与roce.go中的逻辑一致
	defer func() {
		if r := recover(); r != nil {
			logger.Errorf("cmdAdd panic message:%v", r)
			logger.Error(debug.Stack())
		}
	}()

	// 演示不同类型的日志输出
	demonstrateLogging(logger)
	
	// 模拟一些网络操作的日志
	simulateNetworkOperations(logger)
	
	// 演示错误处理
	demonstrateErrorHandling(logger)
}

func demonstrateLogging(logger *logrus.Entry) {
	logger.Debug("This is a debug message - only visible when log level is debug")
	logger.Info("This is an info message")
	logger.Warn("This is a warning message")
	logger.Error("This is an error message")
	
	// 使用WithFields添加结构化字段
	logger.WithFields(logrus.Fields{
		"ipconf": "***********00/24",
		"device": "eth0",
		"mac":    "aa:bb:cc:dd:ee:ff",
	}).Info("ipconf information")
}

func simulateNetworkOperations(logger *logrus.Entry) {
	// 模拟roce.go中的网络操作日志
	masterMac := "aa:bb:cc:dd:ee:ff"
	ifName := "rdma1"
	
	logger.Infof("create ipvlan args masterMac: %s,ifName:%s,tmpName:%s,MTU:%d,ParentIndex:%d", 
		masterMac, ifName, "tmp-rdma1", 1500, 2)
	
	logger.Infof("create ipvlan dev: %s successfully,master:%s", ifName, "eth0")
	
	// 模拟路由添加
	srcIP := "***********00"
	rtable := 200
	logger.Infof("add rule table: %d,src ip: %s", rtable, srcIP)
	logger.Infof("add rule table: %d,oif: %s", rtable, ifName)
	
	// 模拟路由命令
	strDefaultRoute := fmt.Sprintf("ip route add default dev %s via %s src %s table %d onlink", 
		ifName, "***********", srcIP, rtable)
	logger.Infof("add route: %s", strDefaultRoute)
}

func demonstrateErrorHandling(logger *logrus.Entry) {
	// 模拟一些可能的错误情况
	logger.Errorf("loadConf err: %v", fmt.Errorf("failed to parse network configuration"))
	logger.Errorf("AllocateIP failed: %v", fmt.Errorf("no available IP addresses"))
	logger.Errorf("grad file lock error: %s", "resource temporarily unavailable")
	logger.Errorf("create ipvlan error: %v", fmt.Errorf("device already exists"))
	logger.Errorf("setup ipvlan error: %v", fmt.Errorf("failed to set device up"))
	
	// 演示成功的操作
	logger.Info("not rdmaip found.")
	logger.Infof("Allocated RDMAIPAM for pod %s in ns %s for container %s", 
		"default/demo-pod", "/var/run/netns/demo", "container-123")
	logger.Infof("release for pod(%v %v) successfully", "default", "demo-pod")
}

func main() {
	if err := rootCmd.Execute(); err != nil {
		fmt.Printf("Failed to execute command: %v\n", err)
		os.Exit(1)
	}
}
