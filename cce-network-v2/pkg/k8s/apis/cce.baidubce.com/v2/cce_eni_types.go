/*
 * Copyright (c) 2023 Baidu, Inc. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file
 * except in compliance with the License. You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under the
 * License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND,
 * either express or implied. See the License for the specific language governing permissions
 * and limitations under the License.
 *
 */

package v2

import (
	"github.com/baidubce/baiducloud-cce-cni-driver/cce-network-v2/api/v1/models"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object
// +k8s:openapi-gen=false
// +deepequal-gen=false
type ENIList struct {
	metav1.TypeMeta `json:",inline"`
	metav1.ListMeta `json:"metadata"`

	Items []ENI `json:"items"`
}

// +genclient
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object
// +deepequal-gen=false
// +genclient:nonNamespaced
// +kubebuilder:resource:categories={cce},singular="eni",path="enis",scope="Cluster"
// +kubebuilder:storageversion
// +kubebuilder:subresource:status
//
// This columns will be printed by exec `kubectl get enis`
// +kubebuilder:printcolumn:JSONPath=".spec.nodeName",description="eni attached to node",name="Node",type=string
// +kubebuilder:printcolumn:JSONPath=".spec.useMode",description="mode of eni",name="Mode",type=string
// +kubebuilder:printcolumn:JSONPath=".status.CCEStatus",description="status of eni",name="CStatus",type=string
// +kubebuilder:printcolumn:JSONPath=".status.VPCStatus",description="eni status of vpc",name="VStatus",type=string
// +kubebuilder:printcolumn:JSONPath=".status.interfaceIndex",description="interface number on node",name="INumber",type=integer
type ENI struct {
	// +deepequal-gen=false
	metav1.TypeMeta `json:",inline"`
	// +deepequal-gen=false
	metav1.ObjectMeta `json:"metadata,omitempty"`

	Spec ENISpec `json:"spec,omitempty"`
	// +kubebuilder:validation:Optional
	Status ENIStatus `json:"status,omitempty"`
}

type ENISpec struct {
	models.ENI `json:",inline"`

	// ENI 要绑定的节点名
	NodeName string `json:"nodeName"`

	// +kubebuilder:default:=Secondary

	// By default, the secondary IP mode is ENI, and pod IP is
	// the secondary IP of ENI The use scenario of this mode
	// is to use the virtual network device via the veth
	// pair or IPVLAN
	UseMode ENIUseMode `json:"useMode"`

	// RouteTableOffset route policy offset, default 127
	// +kubebuilder:validation:Minimum=0
	// +kubebuilder:default:=127
	RouteTableOffset int `json:"routeTableOffset"`

	// InstallSourceBasedRouting install source based routing, default false
	InstallSourceBasedRouting bool `json:"installSourceBasedRouting,omitempty"`

	// VPCVersion vpc version, default 0
	// data version of vpc, used to determine whether the object needs to be updated
	VPCVersion int64 `json:"vpcVersion,omitempty"`

	// Type eni type, default bcc
	// +kubebuilder:default:=bcc
	Type ENIType `json:"type,omitempty"`

	// BorrowIPCount
	BorrowIPCount int `json:"borrowIPCount,omitempty"`
}

type ENIStatus struct {
	// GatewayIP is the interface's subnet's default route
	//
	// +optional
	GatewayIPv4 string `json:"gatewayIPv4,omitempty"`
	GatewayIPv6 string `json:"gatewayIPv6,omitempty"`

	// InterfaceIndex is the interface index, it used in combination with
	// FirstInterfaceIndex
	//
	// +optional
	InterfaceIndex int `json:"interfaceIndex,omitempty"`

	// InterfaceName is the interface name, it is generated by the agent
	//
	// +optional
	InterfaceName string `json:"interfaceName,omitempty"`

	// Number is the ENI index, it is generated by the agent
	//
	// +optional
	ENIIndex int `json:"index,omitempty"`

	// Endpoint of eni associated In Primary use mode, Pod will directly use the
	// primary IP of ENI, so it needs to move the ENI device directly to the
	// network namespace of Pod.
	// In order to avoid possible failures during the creation of Pod, we need
	// to record the endpoint currently associated with ENI to further find
	// ENI devices
	EndpointReference *ObjectReference `json:"endpointReference,omitempty"`

	// +kubebuilder:default:=Pending
	CCEStatus CCEENIStatus `json:"CCEStatus"`
	// +kubebuilder:validation:MaxItems=20
	CCEStatusChangeLog []ENIStatusChange `json:"CCEStatusChangeLog,omitempty"`
	// +kubebuilder:default:=none
	VPCStatus VPCENIStatus `json:"VPCStatus"`
	// +kubebuilder:validation:MaxItems=20
	VPCStatusChangeLog []ENIStatusChange `json:"VPCStatusChangeLog,omitempty"`

	// VPCVersion vpc version, default 0
	// data version of vpc, used to determine whether the object needs to be updated
	VPCVersion int64 `json:"vpcVersion,omitempty"`

	LendBorrowedIPCount int `json:"lendBorrowedIPCount,omitempty"`
}

// ENIStatusChange history of ENIStatus. This is used to track changes
type ENIStatusChange struct {
	StatusChange `json:",inline"`

	// state
	CCEENIStatus CCEENIStatus `json:"CCEStatus,omitempty"`
	VPCStatus    VPCENIStatus `json:"VPCStatus,omitempty"`
}

type StatusChange struct {
	// +kubebuilder:validation:Enum=ok;failed

	// Code indicate type of status change
	// Enum: [ok failed]
	Code string `json:"code"`

	// Status message
	Message string `json:"message,omitempty"`

	Time metav1.Time `json:"time,omitempty"`
}

type CCEENIStatus string
type VPCENIStatus string

/* vpc 中 ENI 共 4 种状态(https://cloud.baidu.com/doc/VPC/s/6kknfn5m8):
 *     available：创建完成，未挂载
 *     attaching：挂载中
 *     inuse：    已挂载到单机，vpc 认为的可用状态
 *     detaching：卸载中
 */
const (
	// eni is not exist in vpc
	VPCENIStatusNone VPCENIStatus = ""

	// status provider by bce cloud
	VPCENIStatusAvailable VPCENIStatus = "available"
	VPCENIStatusAttaching VPCENIStatus = "attaching"
	VPCENIStatusInuse     VPCENIStatus = "inuse"
	VPCENIStatusDetaching VPCENIStatus = "detaching"
	VPCENIStatusDeleted   VPCENIStatus = "deleted"
)

/* cce 中 ENI 状态:
 *     Pending:      创建 crd 之后的初始状态
 *     Created:      向 VPC 发起创建 ENI 请求成功之后
 *     ReadyInVPC:   attach 成功，VPC 中进入 inuse 状态
 *     ReadyOnNode:  ReadyInVPC 之后单机对 ENI check ok
 *     UsingInPod:   ENI 被 pod 独占使用中
 *     DeletedInVPC: ENI 被从 VPC 中强删后的最终状态
 *
 * 独占 ENI 状态机流转：
 *
 *             创建请求成功(ipam写)             attach后进入inuse状态(ipam写)
 *   Pending -----------------------> Created ---------------------------> ReadyInVPC
 *                                       ^                                      |
 * 	           VPC中强制detach后(ipam写)   |                                      |单机check ok(agent写)
 *                                        |                                     |
 *                                         ---------------------------------    |
 *                                         |                               |    |
 *                  VPC中强删后(ipam写)      |           pod创建后(agent写)   |    v
 *   DeletedInVPC <--------------------- UsingInPod <-------------------- ReadyOnNode --
 *                              |           |                                  ^        |
 *                              |           |                                  |        |
 * 	                            |           |          pod删除后(agent写)       |        |
 *	                            |            ----------------------------------         |
 *                              |                                                       |
 *                               -------------------------------------------------------
 */
const (
	ENIStatusNone        CCEENIStatus = ""
	ENIStatusPending     CCEENIStatus = "Pending"
	ENIStatusReadyOnNode CCEENIStatus = "ReadyOnNode"
	ENIStatusUsingInPod  CCEENIStatus = "UsingInPod"
)

type ENIUseMode string

const (
	// ENIUseModeSecondaryIP Pod IP is the secondary IP of ENI
	ENIUseModeSecondaryIP ENIUseMode = "Secondary"

	// ENIUseModePrimaryIP Pod IP is the primamry IP of ENI
	ENIUseModePrimaryIP ENIUseMode = "Primary"

	// ENIUseModePrimaryWithSecondaryIP Pod IP is the primary interface with secondary IP
	// this mode is only used for ebc
	ENIUseModePrimaryWithSecondaryIP ENIUseMode = "PrimaryWithSecondaryIP"
)

type ObjectReference struct {
	Namespace string `json:"namespace,omitempty"`
	Name      string `json:"name,omitempty"`
	UID       string `json:"uid,omitempty"`
}

type ENIType string

const (
	ENIDefaultBCC ENIType = ""
	ENIForBCC     ENIType = "bcc"
	ENIForBBC     ENIType = "bbc"
	ENIForEBC     ENIType = "ebc"
	ENIForHPC     ENIType = "rdma_roce"
	ENIForERI     ENIType = "elastic_rdma"
)
