apiVersion: apps/v1
kind: Deployment
metadata:
  name: cce-network-operator
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "helm.labels" . | nindent 4 }}
spec:
  replicas: {{ .Values.network.operator.replicaCount }}
  selector:
    matchLabels:
      app.cce.baidubce.com: cce-network-operator
      {{- include "helm.selectorLabels" . | nindent 6 }}

  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        app.cce.baidubce.com: cce-network-operator
        cce.baidubce.com/cniwebhook: disabled
        {{- include "helm.selectorLabels" . | nindent 8 }}
    spec:
      serviceAccountName: cce-cni-v2
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: operator
          image: "{{ .Values.network.operator.image.repository }}/{{ .Values.network.operator.image.name }}:{{ .Values.network.operator.image.tag | default .Chart.AppVersion }}{{ .Values.network.imageSuffix }}"
          imagePullPolicy: {{ .Values.network.operator.image.pullPolicy }}
          command:
            - /bin/cce-network-operator
          args:
            - --config=/etc/cce/network-v2-config.yaml
            - --k8s-namespace={{ .Release.Namespace }}
            - --debug={{ .Values.ccedConfig.debug }}
          env:
            - name: CCE_POD_NAMESPACE
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: metadata.namespace
            - name: CCE_CCE_POD_LABELS
              value: app.cce.baidubce.com=cce-network-agent
            - name: K8S_NODE_NAME
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: spec.nodeName
            - name: CCE_K8S_NAMESPACE
              valueFrom:
                  fieldRef:
                    apiVersion: v1
                    fieldPath: metadata.namespace
            {{- if .Values.ccedConfig.bccEndpoint }}
            - name: BCC_ENDPOINT
              value: "{{ .Values.ccedConfig.bccEndpoint }}"
            {{- end }}  
            {{- if .Values.ccedConfig.bbcEndpoint }}
            - name: BBC_ENDPOINT
              value: "{{ .Values.ccedConfig.bbcEndpoint }}"
            {{- end }}  
            {{- if .Values.ccedConfig.eipEndpoint }}
            - name: EIP_ENDPOINT
              value: "{{ .Values.ccedConfig.eipEndpoint }}"
            {{- end }} 

          ports:
            - name: prometheus
              containerPort: 19962
              protocol: TCP
            - name: api
              containerPort: 19234
              protocol: TCP
          readinessProbe:
            httpGet:
              path: /healthz
              port: api
          resources:
            {{- toYaml .Values.network.operator.resources | nindent 12 }}
          volumeMounts:
            - mountPath: /dev/log
              name: log
            - mountPath: /etc/cce
              name: cce-network-v2-config

        {{- if .Values.network.operator.rdma.enable }}

        {{- end }}

        {{- if .Values.network.operator.webhook.enable }}

        - name: webhook
          image: "{{ .Values.network.operator.image.repository }}/{{ .Values.network.operator.image.name }}:{{ .Values.network.operator.image.tag | default .Chart.AppVersion }}{{ .Values.network.imageSuffix }}"
          imagePullPolicy: {{ .Values.network.operator.image.pullPolicy }}
          command: ["/bin/webhook"]
          args:
            - --validting-webhook-configuration-name={{ .Values.network.operator.webhook.validating }}
            - --mutating-webhook-configuration-name={{ .Values.network.operator.webhook.mutating }}
            - --webhook-service-name=cce-network-v2
            - --webhook-secret-name=cce-network-v2
            - --namespace={{ .Release.Namespace }}
            {{- range $key, $value := .Values.ccedConfig }}
            {{- if eq $key "eni-use-mode"}}
            - --eni-use-mode={{- $value }}
            {{- end }}
            {{- end }}
            - --ipam={{ .Values.ccedConfig.ipam }}
          ports:
            - name: webhook
              containerPort: 18921
              protocol: TCP
          resources:
            {{- toYaml .Values.network.operator.resources | nindent 12 }}
        {{- end }}

      volumes:
        - hostPath:
            path: /dev/log
          name: log
        - configMap:
            defaultMode: 420
            items:
              - key: cced
                path: network-v2-config.yaml
            name: cce-network-v2-config
          name: cce-network-v2-config
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      priorityClassName: system-cluster-critical
      hostNetwork: true
      {{- with .Values.network.operator.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
