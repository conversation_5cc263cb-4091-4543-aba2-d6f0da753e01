
---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.6.2
  creationTimestamp: null
  name: subnets.cce.baidubce.com
spec:
  group: cce.baidubce.com
  names:
    categories:
    - cce
    kind: Subnet
    listKind: SubnetList
    plural: subnets
    shortNames:
    - sbn
    singular: subnet
  scope: Cluster
  versions:
  - additionalPrinterColumns:
    - jsonPath: .spec.availabilityZone
      name: AZ
      type: string
    - description: ipv4 cidr
      jsonPath: .spec.cidr
      name: CIDR4
      type: string
    - description: ipv6 cidr
      jsonPath: .spec.ipv6CIDR
      name: CIDR6
      type: string
    - description: Exclusive
      jsonPath: .spec.exclusive
      name: Exclu
      type: boolean
    - description: available ip number
      jsonPath: .status.availableIPNum
      name: AIP
      type: integer
    name: v1
    schema:
      openAPIV3Schema:
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation
              of an object. Servers should convert recognized schemas to the latest
              internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this
              object represents. Servers may infer this from the endpoint the client
              submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          metadata:
            type: object
          spec:
            properties:
              availabilityZone:
                type: string
              cidr:
                type: string
              description:
                type: string
              exclusive:
                description: "Exclusive subnet flag Exclusive subnet only allows manual
                  IP assignment If a subnet is marked as a Exclusive subnet, it can
                  no longer be used as a subnet for automatic IP allocation \n WARN:
                  Marking as an exclusive subnet only allows you to manually allocate
                  IP. If the default subnet for IPAM is marked as an exclusive subnet,
                  it may cause all pods to fail to allocate IP"
                type: boolean
              id:
                type: string
              ipv6CIDR:
                type: string
              name:
                type: string
              subnetType:
                type: string
              tags:
                additionalProperties:
                  type: string
                type: object
              vpcId:
                type: string
            type: object
          status:
            properties:
              availableIPNum:
                type: integer
              enable:
                type: boolean
              hasNoMoreIP:
                type: boolean
              reason:
                type: string
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
status:
  acceptedNames:
    kind: ""
    plural: ""
  conditions: []
  storedVersions: []
