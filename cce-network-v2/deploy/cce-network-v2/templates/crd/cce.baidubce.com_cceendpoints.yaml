
---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.6.2
  creationTimestamp: null
  name: cceendpoints.cce.baidubce.com
spec:
  group: cce.baidubce.com
  names:
    categories:
    - cce
    kind: CCEEndpoint
    listKind: CCEEndpointList
    plural: cceendpoints
    shortNames:
    - cep
    - ccep
    singular: cceendpoint
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - description: ip type
      jsonPath: .spec.network.ipAllocation.type
      name: Type
      type: string
    - description: ip release type
      jsonPath: .spec.network.ipAllocation.releaseStrategy
      name: Release
      type: string
    - description: Endpoint current state
      jsonPath: .status.state
      name: State
      type: string
    - description: Endpoint ip address
      jsonPath: .status.networking.ips
      name: IPS
      type: string
    - description: Endpoint runing on the node
      jsonPath: .status.networking.node
      name: Node
      type: string
    name: v2
    schema:
      openAPIV3Schema:
        description: CCEEndpoint is the status of pod
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation
              of an object. Servers should convert recognized schemas to the latest
              internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this
              object represents. Servers may infer this from the endpoint the client
              submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          metadata:
            type: object
          spec:
            properties:
              extFeatureGates:
                description: ExtFeatureGates is a set of feature gates to enable or
                  disable specific features like publicIP every feature gate will
                  have its own .status.extFeatureStatus
                items:
                  type: string
                type: array
              external-identifiers:
                description: ExternalIdentifiers is a set of identifiers to identify
                  the endpoint apart from the pod name. This includes container runtime
                  IDs.
                properties:
                  cnidriver:
                    description: device driver name
                    type: string
                  container-id:
                    description: ID assigned by container runtime
                    type: string
                  container-name:
                    description: Name assigned to container
                    type: string
                  external-identifier:
                    description: External network ID, such as the ID of the ENI occupied
                      by the container
                    type: string
                  k8s-namespace:
                    description: K8s namespace for this endpoint
                    type: string
                  k8s-object-id:
                    description: K8s object id to indentifier a unique object
                    type: string
                  k8s-pod-name:
                    description: K8s pod name for this endpoint
                    type: string
                  netns:
                    description: netns use by CNI
                    type: string
                  pod-name:
                    description: K8s pod for this endpoint(Deprecated, use K8sPodName
                      and K8sNamespace instead)
                    type: string
                type: object
              network:
                description: EndpointNetworkSpec Network config for CCE Endpoint
                properties:
                  bindwidth:
                    description: BindwidthOption is the option of bindwidth
                    properties:
                      egress:
                        format: int64
                        type: integer
                      ingress:
                        format: int64
                        type: integer
                      mode:
                        type: string
                    type: object
                  egressPriority:
                    properties:
                      bands:
                        format: int32
                        type: integer
                      dscp:
                        format: int32
                        type: integer
                      priority:
                        type: string
                    type: object
                  ipAllocation:
                    properties:
                      node:
                        description: NodeIP is the IP of the node the endpoint is
                          running on. The IP must be reachable between nodes.
                        type: string
                      pstsName:
                        description: PSTSName is the name of the PSTS This field is
                          only valid in the PSTS mode
                        type: string
                      releaseStrategy:
                        default: TTL
                        description: 'IP address recycling policy TTL: represents
                          the default dynamic IP address recycling policy,default.
                          Never: this policy can only be used in fixed IP scenarios
                          this filed is only valid when the pstsName is empty'
                        enum:
                        - TTL
                        - Never
                        type: string
                      ttlSecondsAfterDeleted:
                        description: TTLSecondsAfterFinished is the TTL duration after
                          this pod has been deleted when using fixed IP mode. This
                          field is only valid in the Fixed mode and the ReleaseStrategy
                          is TTL default is 7d this filed is only valid when the pstsName
                          is empty
                        format: int64
                        type: integer
                      type:
                        default: Elastic
                        description: this filed is only valid then the pstsName is
                          empty
                        enum:
                        - Elastic
                        - Fixed
                        - Manual
                        - Custom
                        - IPAllocTypeNil
                        - PrimaryENI
                        - RDMA
                        type: string
                      useIPV4:
                        type: boolean
                      useIPV6:
                        type: boolean
                    type: object
                type: object
            type: object
          status:
            description: EndpointStatus is the status of a CCE endpoint.
            properties:
              controllers:
                description: Controllers is the list of failing controllers for this
                  endpoint.
                items:
                  description: ControllerStatus is the status of a failing controller.
                  properties:
                    configuration:
                      description: Configuration is the controller configuration
                      properties:
                        error-retry:
                          description: Retry on error
                          type: boolean
                        error-retry-base:
                          description: 'Base error retry back-off time Format: duration'
                          format: int64
                          type: integer
                        interval:
                          description: 'Regular synchronization interval Format: duration'
                          format: int64
                          type: integer
                      type: object
                    name:
                      description: Name is the name of the controller
                      type: string
                    status:
                      description: Status is the status of the controller
                      properties:
                        consecutive-failure-count:
                          format: int64
                          type: integer
                        failure-count:
                          format: int64
                          type: integer
                        last-failure-msg:
                          type: string
                        last-failure-timestamp:
                          type: string
                        last-success-timestamp:
                          type: string
                        success-count:
                          format: int64
                          type: integer
                      type: object
                    uuid:
                      description: UUID is the UUID of the controller
                      type: string
                  type: object
                type: array
              extFeatureStatus:
                additionalProperties:
                  description: ExtFeatureStatus is the status of external feature
                  properties:
                    container-id:
                      description: ID assigned by container runtime
                      type: string
                    data:
                      additionalProperties:
                        type: string
                      description: Data is a set of key-value pairs that can be used
                        to store additional information
                      type: object
                    msg:
                      type: string
                    ready:
                      description: Ready the external feature is ready to use External
                        features are only considered ready when both `container-id``
                        and `ready` are in place ready is only valid when the container-id
                        is equals to `.spec.external-identifiers.container-id`
                      type: boolean
                    updateTime:
                      description: UpdateTime is the time when the status was last
                        updated
                      format: date-time
                      type: string
                  required:
                  - container-id
                  - ready
                  type: object
                description: ExtFeatureStatus is a set of feature status to indicate
                  the status of specific features like publicIP
                type: object
              external-identifiers:
                description: ExternalIdentifiers is a set of identifiers to identify
                  the endpoint apart from the pod name. This includes container runtime
                  IDs.
                properties:
                  cnidriver:
                    description: device driver name
                    type: string
                  container-id:
                    description: ID assigned by container runtime
                    type: string
                  container-name:
                    description: Name assigned to container
                    type: string
                  external-identifier:
                    description: External network ID, such as the ID of the ENI occupied
                      by the container
                    type: string
                  k8s-namespace:
                    description: K8s namespace for this endpoint
                    type: string
                  k8s-object-id:
                    description: K8s object id to indentifier a unique object
                    type: string
                  k8s-pod-name:
                    description: K8s pod name for this endpoint
                    type: string
                  netns:
                    description: netns use by CNI
                    type: string
                  pod-name:
                    description: K8s pod for this endpoint(Deprecated, use K8sPodName
                      and K8sNamespace instead)
                    type: string
                type: object
              log:
                description: Log is the list of the last few warning and error log
                  entries
                items:
                  description: "EndpointStatusChange Indication of a change of status
                    \n swagger:model EndpointStatusChange"
                  properties:
                    code:
                      description: 'Code indicate type of status change Enum: [ok
                        failed]'
                      type: string
                    message:
                      description: Status message
                      type: string
                    state:
                      description: state
                      type: string
                    timestamp:
                      description: Timestamp when status change occurred
                      type: string
                  type: object
                type: array
              matchExpressions:
                description: A list of node selector requirements by node's labels.
                items:
                  description: A node selector requirement is a selector that contains
                    values, a key, and an operator that relates the key and values.
                  properties:
                    key:
                      description: The label key that the selector applies to.
                      type: string
                    operator:
                      description: Represents a key's relationship to a set of values.
                        Valid operators are In, NotIn, Exists, DoesNotExist. Gt, and
                        Lt.
                      type: string
                    values:
                      description: An array of string values. If the operator is In
                        or NotIn, the values array must be non-empty. If the operator
                        is Exists or DoesNotExist, the values array must be empty.
                        If the operator is Gt or Lt, the values array must have a
                        single element, which will be interpreted as an integer. This
                        array is replaced during a strategic merge patch.
                      items:
                        type: string
                      type: array
                  required:
                  - key
                  - operator
                  type: object
                type: array
              networking:
                description: Networking is the networking properties of the endpoint.
                properties:
                  addressing:
                    description: IP4/6 addresses assigned to this Endpoint
                    items:
                      description: AddressPair is is a par of IPv4 and/or IPv6 address.
                      properties:
                        cidr:
                          description: CIDRs is a list of all CIDRs to which the IP
                            has direct access to. This is primarily useful if the
                            IP has been allocated out of a VPC subnet range and the
                            VPC provides routing to a set of CIDRs in which the IP
                            is routable.
                          items:
                            type: string
                          type: array
                        family:
                          description: Family is the kind of address. E.g. "4" or
                            "6".
                          type: string
                        gateway:
                          type: string
                        interface:
                          type: string
                        ip:
                          type: string
                        subnet:
                          type: string
                        vpcID:
                          type: string
                      required:
                      - family
                      type: object
                    type: array
                  ips:
                    description: IPs is the list of IP addresses assigned to this
                      Endpoint
                    type: string
                  node:
                    description: NodeIP is the IP of the node the endpoint is running
                      on. The IP must be reachable between nodes.
                    type: string
                type: object
              state:
                description: State is the state of the endpoint.
                type: string
            type: object
        required:
        - metadata
        - spec
        type: object
    served: true
    storage: true
    subresources: {}
status:
  acceptedNames:
    kind: ""
    plural: ""
  conditions: []
  storedVersions: []
