# Default values for helm.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.
network:
  # 镜像后缀，如 -arm64
  imageSuffix: ""
  operator:
    replicaCount: 2
    image:
      # 注意 repository + name 才是完整的镜像名
      repository: registry.baidubce.com/cce-plugin-pro
      # 使用vpc-eni作为operator镜像
      name: cce-network-operator-vpc-eni
      pullPolicy: Always
      # Overrides the image tag whose default is the chart appVersion.
      tag: ""
    resources: { }
    rdma:
      enable: true
    webhook:
      mutating: cce-network-v2-mutating-webhook
      validating: ""
      enable: true
    affinity:
      nodeAffinity:
        preferredDuringSchedulingIgnoredDuringExecution:
        - weight: 1
          preference:
            matchExpressions:
            - key: cluster-role
              operator: In
              values: ["master"]    
  agent:
    name: agent
    image:
      repository: registry.baidubce.com/cce-plugin-pro/cce-network-agent
      pullPolicy: Always
      # Overrides the image tag whose default is the chart appVersion.
      tag: ""
    resources: { }
    affinity:
      nodeAffinity:
        requiredDuringSchedulingIgnoredDuringExecution:
          nodeSelectorTerms:
            - matchExpressions:
              - key: type
                operator: NotIn
                values:
                  - virtual-kubelet
tolerations:
  - operator: "Exists"


imagePullSecrets: [ ]
nameOverride: ""
fullnameOverride: ""

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: { }
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

podAnnotations: { }

podSecurityContext: { }
# fsGroup: 2000

securityContext: { }
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
# runAsUser: 1000

service:
  type: ClusterIP
  port: 80

resources: { }
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  # limits:
  #   cpu: 100m
  #   memory: 128Mi
  # requests:
  #   cpu: 100m
#   memory: 128Mi

nodeSelector: { }

affinity: { }

extplugins: {}
  # cilium-cni:
  #   type: cilium-cni
  # sbr-eip:
  #   type: sbr-eip  

# cce守护进程配置
ccedConfig:
  # operator BCE VPC 配置
  cce-cluster-id: ""
  bce-cloud-vpc-id: "" 
  bce-cloud-access-key: ""
  bce-cloud-secure-key: ""
  # 云的可用区，用于选择Cloud API的地址
  bce-cloud-country: cn
  bce-cloud-region: ""
  bce-cloud-host: ""

  # 开启debug模式(会多打日志)
  debug: false
  # 在非k8s环境运行使用
  # k8s-kubeconfig-path: /run/kubeconfig
  k8s-client-burst: 100
  k8s-client-qps: 50
  k8s-api-discovery: false
  leader-election-lease-duration: 60s
  leader-election-renew-deadline: 30s
  # 启用自动创建cce node资源
  skip-manager-node-labels:
    type: virtual-kubelet
  auto-create-network-resource-set-resource: true
  enable-monitor: false
  nodes-gc-interval: 30s
  skip-crd-creation: true
  mtu: 1500
  # 启动ipv4
  enable-ipv4: true
  enable-ipv6: false
  enable-rdma: false
  # api 限流配置
  default-api-burst: 100
  default-api-qps: 50
  default-api-timeout: 30s
  api-rate-limit:
    # bce api 限流
    bcecloud/apis/v1/BatchAddPrivateIP: "rate-limit:5/1s,rate-burst:10,max-wait-duration:15s,parallel-requests:5,log:true"
    bcecloud/apis/v1/BatchDeletePrivateIP: "rate-limit:5/1s,rate-burst:10,max-wait-duration:15s,parallel-requests:5,log:true"
    bcecloud/apis/v1/AttachENI: "rate-limit:5/1s,rate-burst:5,max-wait-duration:30s,parallel-requests:5,log:true"
    bcecloud/apis/v1/CreateENI: "rate-limit:5/1s,rate-burst:5,max-wait-duration:30s,parallel-requests:5,log:true"
    bcecloud/apis/v1/DescribeSubnet: "rate-limit:5/1s,rate-burst:5,max-wait-duration:30s,parallel-requests:5"
    bcecloud/apis/v1/StatENI: "rate-limit:10/1s,rate-burst:15,max-wait-duration:30s,parallel-requests:10"

  # 调试
  pprof: false
  pprof-port: 14386
  health-port: 19879
  gops-port: 19891
  prometheus-serve-addr: ":19962"
  # 开启operator metrics
  enable-metrics: true
  operator-prometheus-serve-addr: ":19965"
  operator-api-serve-addr: ":19234"
  log-driver: syslog
  log-opt: |
    {"syslog.level":"info","syslog.facility":"local5"}
  # cni 配置，从指定文件读取，写入到另一个路径
  write-cni-conf-when-ready: ""

  # ipam 模式. privatecloudbase: 私有云底座;vpc-eni: BCE VPC ENI
  ipam: vpc-eni
  endpoint-gc-interval: 30s
  # vpc 资源同步周期
  resource-resync-interval: 20s
  # 资源重新同步的并发协程数
  resource-resync-workers: 64
  nrs-resource-resync-workers: 500
  rdma-resource-resync-workers: 500
  eni-resource-resync-workers: 100
  subnet-resource-resync-workers: 10

  # agent vpc-eni 配置
  # 禁用ENI CRD
  disable-eni-crd: false
  # ENI使用模式：Secondary：辅助IP；Primary：主IP（独占ENI）
  eni-use-mode: Secondary
  eni-install-source-based-routing: true
  eni-subnet-ids: ""
  eni-security-group-ids: ""
  eni-enterprise-security-group-ids: ""

  eni-pre-allocate-num: 1
  eni-route-table-offset: 127
  # 启用burstable ENI池，默认使用保持最少 1 个 ENI 空闲
  burstable-mehrfach-eni: 1
  # ippool 优化配置
  ippool-min-allocate-ips: 10
  ippool-pre-allocate: 2
  ippool-max-above-watermark: 24
  # 自动释放超限的IP，默认不开启释放（开启 burstable ENI 的节点默认默认不会开启 IP 释放）
  release-excess-ips: false
  excess-ip-release-delay: 180

  cce-endpoint-gc-interval: 20s
  # cni 固定IP申请等待超时时间
  fixed-ip-allocate-timeout: 30s
  # 启用对远程固定IP的回收功能(开启后当系统发现远程记录有固定IP,但是k8s中没有与之对应的endpoint时,会删除远程固定IP)
  enable-remote-fixed-ip-gc: false
  # cni IP申请请求的超时时间
  ip-allocation-timeout: 30s
  # pod删除后固定ip保留时间
  fixed-ip-ttl-duration: 87600h

    # cni 接口限流
  # 扩展插件列表
  ext-cni-plugins:
  - "endpoint-probe"
  # - "cilium-cni"
  # - "portmap"